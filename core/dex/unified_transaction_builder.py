#!/usr/bin/env python3
"""
Unified Transaction Builder
Consolidates all transaction building logic into a single, reliable component.
Replaces multiple conflicting transaction builders with one optimized implementation.
"""

import asyncio
import logging
import base64
import time
from typing import Dict, Any, Optional
from solders.transaction import VersionedTransaction
from solders.keypair import Keypair

logger = logging.getLogger(__name__)

class UnifiedTransactionBuilder:
    """
    Unified transaction builder that consolidates all transaction building logic.
    Uses Jupiter API for real DEX swaps with QuickNode/Jito/Helius execution.
    """

    def __init__(self, wallet_address: str, keypair: Optional[Keypair] = None):
        """
        Initialize unified transaction builder.

        Args:
            wallet_address: Wallet address
            keypair: Keypair for signing (optional)
        """
        self.wallet_address = wallet_address
        self.keypair = keypair

        # 🚨 JUPITER-FREE: Use native swap builder with QuickNode/Jito bundles
        self.native_builder = None

        logger.info(f"🔨 JUPITER-FREE Unified Transaction Builder initialized for wallet: {wallet_address}")

    async def initialize(self):
        """Initialize the transaction builder."""
        try:
            # 🚨 JUPITER-FREE: Initialize native swap builder with <PERSON>Node/Jito
            from core.dex.native_swap_builder import NativeSwapBuilder
            self.native_builder = NativeSwapBuilder(self.wallet_address, self.keypair)
            await self.native_builder.initialize()
            logger.info("✅ JUPITER-FREE: Native swap builder initialized with QuickNode/Jito bundles")

        except Exception as e:
            logger.error(f"❌ Error initializing JUPITER-FREE transaction builder: {e}")
            raise

    async def build_swap_transaction(self, signal: Dict[str, Any]) -> Optional[VersionedTransaction]:
        """
        Build a real swap transaction from a trading signal using native builder.

        Args:
            signal: Trading signal containing action, market, size, etc.

        Returns:
            Signed VersionedTransaction or None if failed
        """
        try:
            logger.info(f"🔨 Building JUPITER-FREE transaction for {signal.get('market', 'Unknown')}")

            if not self.native_builder:
                await self.initialize()

            if not self.keypair:
                logger.error("❌ No keypair available for signing")
                return None

            # 🚨 JUPITER-FREE: Use native swap builder for transaction building
            transaction = await self.native_builder.build_and_sign_transaction(signal)

            if transaction:
                logger.info("✅ JUPITER-FREE: Native transaction built and signed successfully")
                return transaction
            else:
                logger.error("❌ JUPITER-FREE: Native transaction building failed")
                return None

        except Exception as e:
            logger.error(f"❌ Error building JUPITER-FREE transaction: {e}")
            return None

    async def build_and_sign_transaction(self, signal: Dict[str, Any]) -> Optional[VersionedTransaction]:
        """
        Build and sign a transaction from a trading signal.

        Args:
            signal: Trading signal

        Returns:
            Signed VersionedTransaction or None if failed
        """
        return await self.build_swap_transaction(signal)

    def get_transaction_info(self, signal: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get transaction information for a signal.

        Args:
            signal: Trading signal

        Returns:
            Transaction information
        """
        action = signal.get('action', 'BUY')
        size = signal.get('size', 0.01)
        price = signal.get('price', 100.0)

        if action == 'BUY':
            input_token = 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'  # USDC
            output_token = 'So11111111111111111111111111111111111111112'   # SOL
            input_amount = int(size * price * 1_000_000)  # USDC amount
            estimated_output = int(size * 1_000_000_000)  # SOL amount
        else:
            input_token = 'So11111111111111111111111111111111111111112'   # SOL
            output_token = 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'  # USDC
            input_amount = int(size * 1_000_000_000)  # SOL amount
            estimated_output = int(size * price * 1_000_000)  # USDC amount

        return {
            'execution_type': 'native_transfer',
            'input_token': input_token,
            'output_token': output_token,
            'input_amount': input_amount,
            'estimated_output': estimated_output,
            'min_output': int(estimated_output * 0.995),  # 0.5% slippage
            'slippage_bps': 50
        }

    async def close(self):
        """Close the transaction builder."""
        if self.native_builder and hasattr(self.native_builder, 'close'):
            await self.native_builder.close()
        logger.info("✅ JUPITER-FREE unified transaction builder closed")
