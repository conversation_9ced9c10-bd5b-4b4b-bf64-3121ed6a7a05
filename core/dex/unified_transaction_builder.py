#!/usr/bin/env python3
"""
Unified Transaction Builder
Consolidates all transaction building logic into a single, reliable component.
Replaces multiple conflicting transaction builders with one optimized implementation.
"""

import asyncio
import logging
import base64
import time
from typing import Dict, Any, Optional
from solders.transaction import VersionedTransaction
from solders.keypair import Keypair

logger = logging.getLogger(__name__)

class UnifiedTransactionBuilder:
    """
    Unified transaction builder that consolidates all transaction building logic.
    Uses Jupiter API for real DEX swaps with QuickNode/Jito/Helius execution.
    """

    def __init__(self, wallet_address: str, keypair: Optional[Keypair] = None):
        """
        Initialize unified transaction builder.

        Args:
            wallet_address: Wallet address
            keypair: Keypair for signing (optional)
        """
        self.wallet_address = wallet_address
        self.keypair = keypair

        # Initialize Jupiter client for real swaps
        self.jupiter_client = None

        logger.info(f"🔨 Unified Transaction Builder initialized for wallet: {wallet_address}")

    async def initialize(self):
        """Initialize the transaction builder."""
        try:
            # Initialize Jupiter client for real swaps
            from phase_4_deployment.utils.modern_jupiter_client import ModernJupiterClient
            self.jupiter_client = ModernJupiterClient()
            logger.info("✅ Jupiter client initialized for real swaps")

        except Exception as e:
            logger.error(f"❌ Error initializing transaction builder: {e}")
            raise

    async def build_swap_transaction(self, signal: Dict[str, Any]) -> Optional[VersionedTransaction]:
        """
        Build a real swap transaction from a trading signal.

        Args:
            signal: Trading signal containing action, market, size, etc.

        Returns:
            Signed VersionedTransaction or None if failed
        """
        try:
            logger.info(f"🔨 Building REAL swap transaction for {signal.get('market', 'Unknown')}")

            if not self.jupiter_client:
                await self.initialize()

            if not self.keypair:
                logger.error("❌ No keypair available for signing")
                return None

            # Extract signal parameters
            action = signal.get('action', 'BUY')
            market = signal.get('market', 'SOL-USDC')
            size = signal.get('size', 0.01)
            price = signal.get('price', 100.0)

            # Determine token mints based on action
            if action == 'BUY':
                # BUY SOL with USDC
                input_mint = 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'  # USDC
                output_mint = 'So11111111111111111111111111111111111111112'   # SOL
                # Convert SOL amount to USDC amount for input
                input_amount = int(size * price * 1_000_000)  # USDC has 6 decimals
            else:
                # SELL SOL for USDC
                input_mint = 'So11111111111111111111111111111111111111112'   # SOL
                output_mint = 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'  # USDC
                input_amount = int(size * 1_000_000_000)  # SOL has 9 decimals

            logger.info(f"🌊 {action}: {input_amount} {input_mint[:8]}... → {output_mint[:8]}...")

            # Get Jupiter quote
            quote = await self.jupiter_client.get_optimized_quote(
                input_mint=input_mint,
                output_mint=output_mint,
                amount=input_amount,
                slippage_bps=50
            )

            if not quote:
                logger.error("❌ Failed to get Jupiter quote")
                return None

            # Get Jupiter swap transaction
            swap_response = await self.jupiter_client.build_optimized_transaction(
                quote=quote,
                user_public_key=str(self.keypair.pubkey())
            )

            if not swap_response:
                logger.error("❌ Failed to get Jupiter swap transaction")
                return None

            # Parse the transaction bytes (build_optimized_transaction returns bytes directly)
            versioned_tx = VersionedTransaction.from_bytes(swap_response)

            # Sign the transaction
            versioned_tx.sign([self.keypair])

            logger.info("✅ REAL swap transaction built and signed via Jupiter")
            return versioned_tx

        except Exception as e:
            logger.error(f"❌ Error building swap transaction: {e}")
            return None

    async def build_and_sign_transaction(self, signal: Dict[str, Any]) -> Optional[VersionedTransaction]:
        """
        Build and sign a transaction from a trading signal.

        Args:
            signal: Trading signal

        Returns:
            Signed VersionedTransaction or None if failed
        """
        return await self.build_swap_transaction(signal)

    def get_transaction_info(self, signal: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get transaction information for a signal.

        Args:
            signal: Trading signal

        Returns:
            Transaction information
        """
        action = signal.get('action', 'BUY')
        size = signal.get('size', 0.01)
        price = signal.get('price', 100.0)

        if action == 'BUY':
            input_token = 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'  # USDC
            output_token = 'So11111111111111111111111111111111111111112'   # SOL
            input_amount = int(size * price * 1_000_000)  # USDC amount
            estimated_output = int(size * 1_000_000_000)  # SOL amount
        else:
            input_token = 'So11111111111111111111111111111111111111112'   # SOL
            output_token = 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'  # USDC
            input_amount = int(size * 1_000_000_000)  # SOL amount
            estimated_output = int(size * price * 1_000_000)  # USDC amount

        return {
            'execution_type': 'jupiter_swap',
            'input_token': input_token,
            'output_token': output_token,
            'input_amount': input_amount,
            'estimated_output': estimated_output,
            'min_output': int(estimated_output * 0.995),  # 0.5% slippage
            'slippage_bps': 50
        }

    async def close(self):
        """Close the transaction builder."""
        if self.jupiter_client and hasattr(self.jupiter_client, 'close'):
            await self.jupiter_client.close()
        logger.info("✅ Unified transaction builder closed")
