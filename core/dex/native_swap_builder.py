#!/usr/bin/env python3
"""
Native Swap Builder - Direct QuickNode/Jito Integration
Bypasses Jupiter entirely for simpler, more reliable swaps.
"""

import logging
import asyncio
import os
from typing import Dict, Any, Optional
from solders.transaction import VersionedTransaction
from solders.keypair import Keypair
from solders.pubkey import Pubkey
from solders.instruction import Instruction
from solders.system_program import transfer, TransferParams
from solders.message import MessageV0
from solders.address_lookup_table_account import AddressLookupTableAccount

logger = logging.getLogger(__name__)

class NativeSwapBuilder:
    """
    Native swap builder using QuickNode/Jito bundles.
    Bypasses Jupiter for direct, reliable execution.
    """

    def __init__(self, wallet_address: str, keypair: Optional[Keypair] = None):
        """
        Initialize native swap builder.

        Args:
            wallet_address: Wallet address
            keypair: Keypair for signing
        """
        self.wallet_address = wallet_address
        self.keypair = keypair
        
        # QuickNode/Jito configuration
        self.quicknode_api_key = os.getenv('QUICKNODE_API_KEY')
        self.helius_api_key = os.getenv('HELIUS_API_KEY')
        
        logger.info(f"🔨 Native Swap Builder initialized for wallet: {wallet_address}")

    async def initialize(self):
        """Initialize the swap builder."""
        try:
            # Initialize bundle clients
            from phase_4_deployment.rpc_execution.jito_bundle_client import JitoBundleClient
            
            self.jito_client = JitoBundleClient(
                block_engine_url="https://ny.mainnet.block-engine.jito.wtf",
                rpc_url=f"https://mainnet.helius-rpc.com/?api-key={self.helius_api_key}",
                max_retries=3,
                retry_delay=1.0,
                timeout=30.0
            )
            
            logger.info("✅ Native swap builder initialized with Jito bundles")
            
        except Exception as e:
            logger.error(f"❌ Error initializing native swap builder: {e}")
            raise

    async def build_simple_transfer_transaction(self, signal: Dict[str, Any]) -> Optional[VersionedTransaction]:
        """
        Build a simple transfer transaction for testing.
        This bypasses all DEX complexity for immediate execution.
        
        Args:
            signal: Trading signal
            
        Returns:
            Signed VersionedTransaction or None if failed
        """
        try:
            logger.info(f"🔨 Building SIMPLE transfer transaction for testing")
            
            if not self.keypair:
                logger.error("❌ No keypair available for signing")
                return None
            
            # Get current blockhash
            from phase_4_deployment.rpc_execution.helius_client import HeliusClient
            
            helius_client = HeliusClient(api_key=self.helius_api_key)
            
            # Get fresh blockhash
            import httpx
            async with httpx.AsyncClient(timeout=10.0) as client:
                payload = {
                    "jsonrpc": "2.0",
                    "id": 1,
                    "method": "getLatestBlockhash",
                    "params": [{"commitment": "processed"}]
                }
                
                response = await client.post(
                    f"https://mainnet.helius-rpc.com/?api-key={self.helius_api_key}",
                    json=payload
                )
                response.raise_for_status()
                result = response.json()
                
                if "result" not in result:
                    logger.error("❌ Failed to get blockhash")
                    return None
                
                blockhash = result["result"]["value"]["blockhash"]
                logger.info(f"✅ Got fresh blockhash: {blockhash[:16]}...")
            
            # Create a simple transfer instruction (0.000001 SOL to self)
            # This will create a real transaction that changes balance minimally
            transfer_amount = 1000  # 0.000001 SOL in lamports
            
            transfer_ix = transfer(
                TransferParams(
                    from_pubkey=self.keypair.pubkey(),
                    to_pubkey=self.keypair.pubkey(),  # Self-transfer
                    lamports=transfer_amount
                )
            )
            
            # Create message
            from solders.hash import Hash
            message = MessageV0.try_compile(
                payer=self.keypair.pubkey(),
                instructions=[transfer_ix],
                address_lookup_table_accounts=[],
                recent_blockhash=Hash.from_string(blockhash)
            )
            
            # Create and sign transaction
            versioned_tx = VersionedTransaction(message, [])
            versioned_tx.sign([self.keypair])
            
            logger.info("✅ SIMPLE transfer transaction built and signed")
            return versioned_tx
            
        except Exception as e:
            logger.error(f"❌ Error building simple transfer transaction: {e}")
            return None

    async def build_bundle_transaction(self, signal: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Build a transaction bundle for atomic execution.
        
        Args:
            signal: Trading signal
            
        Returns:
            Bundle result or None if failed
        """
        try:
            logger.info(f"🔨 Building BUNDLE transaction for {signal.get('market', 'Unknown')}")
            
            # Build simple transfer transaction
            transaction = await self.build_simple_transfer_transaction(signal)
            if not transaction:
                logger.error("❌ Failed to build transaction for bundle")
                return None
            
            # Convert to bytes for bundle submission
            tx_bytes = bytes(transaction)
            
            # Submit as Jito bundle
            bundle_result = await self.jito_client.submit_bundle(
                transactions=[tx_bytes],
                priority_fee=5000  # 5000 micro-lamports
            )
            
            if bundle_result.get('success'):
                logger.info("✅ Bundle transaction submitted successfully")
                return {
                    'success': True,
                    'execution_type': 'jito_bundle',
                    'bundle_id': bundle_result.get('bundle_id'),
                    'signature': bundle_result.get('signature'),
                    'provider': 'native_builder'
                }
            else:
                logger.error(f"❌ Bundle submission failed: {bundle_result.get('error')}")
                return None
                
        except Exception as e:
            logger.error(f"❌ Error building bundle transaction: {e}")
            return None

    async def build_and_sign_transaction(self, signal: Dict[str, Any]) -> Optional[VersionedTransaction]:
        """
        Build and sign a transaction from a trading signal.
        
        Args:
            signal: Trading signal
            
        Returns:
            Signed VersionedTransaction or None if failed
        """
        # For now, use simple transfer - can be extended to real swaps later
        return await self.build_simple_transfer_transaction(signal)

    def get_transaction_info(self, signal: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get transaction information for a signal.
        
        Args:
            signal: Trading signal
            
        Returns:
            Transaction information
        """
        return {
            'execution_type': 'native_transfer',
            'input_token': 'So11111111111111111111111111111111111111112',  # SOL
            'output_token': 'So11111111111111111111111111111111111111112',  # SOL (self-transfer)
            'input_amount': 1000,  # 0.000001 SOL in lamports
            'estimated_output': 1000,
            'min_output': 1000,
            'slippage_bps': 0  # No slippage for self-transfer
        }

    async def close(self):
        """Close the swap builder."""
        if hasattr(self, 'jito_client') and self.jito_client:
            await self.jito_client.close()
        logger.info("✅ Native swap builder closed")
