# Synergy7 Trading System Production Configuration

# Core configuration
core:
  enabled: true
  binary_path: "bin/carbon_core"
  log_level: "info"
  max_memory_mb: 2048
  worker_threads: 8
  update_interval_ms: 100

# Market Microstructure Settings
market_microstructure:
  enabled: true
  markets:
    - "SOL-USDC"
    - "JTO-USDC"
    - "BONK-USDC"
  order_book_depth: 20
  update_interval_ms: 100
  impact_window_size: 50
  liquidity_threshold: 10000

# API Settings
apis:
  helius:
    enabled: true
    api_key: ${HELIUS_API_KEY}
    rpc_endpoint: "https://mainnet.helius-rpc.com/?api-key=${HELIUS_API_KEY}"
    ws_endpoint: "wss://mainnet.helius-rpc.com/?api-key=${HELIUS_API_KEY}"

  birdeye:
    enabled: true
    api_key: ${BIRDEYE_API_KEY}
    endpoint: "https://api.birdeye.so/v1"

  jito:
    enabled: true
    rpc_url: "https://mainnet.block.jito.io"
    shredstream_url: "wss://mainnet.shredstream.jito.io/stream"
    keypair_path: "keys/jito_keypair.json"

# RPC Settings
rpc:
  endpoint: "https://mainnet.helius-rpc.com/?api-key=${HELIUS_API_KEY}"
  commitment: "confirmed"
  max_retries: 5
  retry_delay_ms: 1000

# Wallet Settings
wallet:
  address: ${WALLET_ADDRESS}
  keypair_path: "keys/wallet_keypair.json"
  max_transaction_fee: 10000  # in lamports

# Signal Generation Settings
signal_generation:
  enabled: true
  update_interval_ms: 1000
  publish_interval_ms: 1000

# Strategy Runner Settings
strategy_runner:
  enabled: true
  update_interval_ms: 1000
  publish_interval_ms: 1000

# Risk Management Settings
risk_management:
  enabled: true
  update_interval_ms: 1000
  publish_interval_ms: 1000
  metrics_interval_ms: 5000

  # Position Sizer
  position_sizer:
    max_position_size: 0.1
    min_position_size: 0.01
    volatility_scaling: true
    volatility_lookback: 20
    position_size_increment: 0.01
    risk_per_trade: 0.01
    atr_multiplier: 2.0

  # Stop Loss Manager
  stop_loss:
    trailing_enabled: true
    trailing_activation_pct: 0.01
    trailing_distance_pct: 0.02
    volatility_multiplier: 2.0
    time_based_widening: true
    widening_factor: 0.001

  # Portfolio Limits
  portfolio_limits:
    max_portfolio_exposure: 0.8
    max_single_market_exposure: 0.3
    max_correlated_exposure: 0.5
    max_daily_drawdown: 0.05
    max_weekly_drawdown: 0.1
    max_monthly_drawdown: 0.15

  # Circuit Breaker
  circuit_breaker:
    enabled: true
    max_consecutive_losses: 3
    max_daily_loss_pct: 0.05
    max_drawdown_pct: 0.1
    cooldown_minutes: 60
    volatility_threshold: 0.05
    api_failure_threshold: 5

  # Legacy parameters (for backward compatibility)
  max_position_size: 0.1
  max_exposure: 0.5
  max_drawdown: 0.1
  var_threshold: 0.05

# Transaction Preparation Settings
transaction_preparation:
  enabled: true
  update_interval_ms: 1000
  publish_interval_ms: 1000

# Transaction Execution Settings
transaction_execution:
  enabled: true
  update_interval_ms: 1000
  publish_interval_ms: 1000
  simulation_enabled: false  # LIVE TRADING: No simulation in production
  dry_run: false  # LIVE TRADING: No dry run in production

# Communication Settings
communication:
  protocol: "zeromq"
  zeromq:
    pub_endpoint: "tcp://127.0.0.1:5555"
    sub_endpoint: "tcp://127.0.0.1:5556"
    req_endpoint: "tcp://127.0.0.1:5557"
  message_format: "json"
  max_message_size: 1048576
  heartbeat_interval_ms: 1000

# Monitoring Settings
monitoring:
  enabled: true
  log_level: "info"
  metrics_interval_ms: 5000
  health_check_interval_ms: 10000
  telegram_alerts: true
  telegram_chat_id: ${TELEGRAM_CHAT_ID}
  telegram_bot_token: ${TELEGRAM_BOT_TOKEN}

# Deployment Settings
deployment:
  streamlit:
    port: 8501
    headless: false
  health_server:
    port: 8080
    host: "0.0.0.0"
  circuit_breaker:
    enabled: true
    failure_threshold: 5
    reset_timeout_seconds: 300
  retry_policy:
    max_retries: 3
    backoff_factor: 2
    max_backoff_seconds: 30

# Strategies
strategies:
  - name: "momentum_sol_usdc"
    type: "momentum"
    enabled: true
    markets:
      - "SOL-USDC"
    weight: 1.0
    parameters:
      window_size: 5  # Optimized value
      threshold: 0.005  # Optimized value
      max_value: 0.05
      smoothing_factor: 0.1
      max_position_size: 0.1

  - name: "order_book_imbalance_sol_usdc"
    type: "order_book_imbalance"
    enabled: true
    markets:
      - "SOL-USDC"
    weight: 1.0
    parameters:
      window_size: 20
      threshold: 0.1
      max_value: 0.5
      depth: 10
      smoothing_factor: 0.2
      max_position_size: 0.1

  - name: "momentum_jto_usdc"
    type: "momentum"
    enabled: true
    markets:
      - "JTO-USDC"
    weight: 1.0
    parameters:
      window_size: 5  # Optimized value
      threshold: 0.005  # Optimized value
      max_value: 0.05
      smoothing_factor: 0.1
      max_position_size: 0.1

  - name: "order_book_imbalance_jto_usdc"
    type: "order_book_imbalance"
    enabled: true
    markets:
      - "JTO-USDC"
    weight: 1.0
    parameters:
      window_size: 20
      threshold: 0.1
      max_value: 0.5
      depth: 10
      smoothing_factor: 0.2
      max_position_size: 0.1

  - name: "momentum_bonk_usdc"
    type: "momentum"
    enabled: true
    markets:
      - "BONK-USDC"
    weight: 1.0
    parameters:
      window_size: 5  # Optimized value
      threshold: 0.005  # Optimized value
      max_value: 0.05
      smoothing_factor: 0.1
      max_position_size: 0.1

  # Mean Reversion Strategy - DISABLED
  - name: "mean_reversion_sol_usdc"
    type: "mean_reversion"
    enabled: false  # Explicitly disabled as per strategy_finder.md directive
    markets:
      - "SOL-USDC"
    weight: 0.0  # Zero weight to ensure it's not used
    parameters:
      window_size: 30
      std_dev: 2.0
      mean_window: 100
      max_position_size: 0.0  # Zero position size to ensure it can't trade
