#!/usr/bin/env python3
"""
GUI Dashboard Application

This module provides a Streamlit-based dashboard for monitoring
trading activities and strategy performance.
"""

import os
import json
import logging
import asyncio
import pandas as pd
import streamlit as st
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

# REMOVED: Carbon Core simulation components - LIVE TRADING ONLY

# Import components
import sys

# Add the components directory to the Python path
components_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'components')
if components_dir not in sys.path:
    sys.path.append(components_dir)

from components.strategy_viewer import render_strategy_viewer
from components.live_monitor import render_live_monitor
from components.pnl_graphs import render_pnl_graphs

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            'output', 'dashboard_log.txt'
        )),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('gui_dashboard')

# Set page config
st.set_page_config(
    page_title="RWA Trading System Live Dashboard",
    page_icon="🚀",
    layout="wide",
    initial_sidebar_state="expanded"
)

def load_data() -> Dict[str, Any]:
    """
    Load data for the dashboard.

    Returns:
        Dictionary containing all data needed for the dashboard
    """
    data = {}

    # Base directory for data files
    base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    output_dir = os.path.join(base_dir, 'output')

    try:
        # Load live trading signals from current session
        live_session_path = os.path.join('output', 'live_production', 'dashboard', 'current_session_summary.json')
        if os.path.exists(live_session_path):
            with open(live_session_path, 'r') as f:
                session_data = json.load(f)
                data['enriched_signals'] = {'signals': session_data.get('recent_signals', [])}
        else:
            data['enriched_signals'] = {'signals': []}

        # Load live trading transaction history
        import glob
        trade_files = glob.glob("output/live_production/trades/trade_*.json")
        trade_files.sort()

        transactions = []
        for trade_file in trade_files[-10:]:  # Last 10 trades
            try:
                with open(trade_file, 'r') as f:
                    trade_data = json.load(f)
                    transactions.append({
                        'timestamp': trade_data.get('timestamp', ''),
                        'signature': trade_data.get('signature', ''),
                        'status': 'confirmed' if trade_data.get('success', False) else 'failed',
                        'action': trade_data.get('action', ''),
                        'market': trade_data.get('market', ''),
                        'size': trade_data.get('size', 0.0)
                    })
            except:
                continue

        data['tx_history'] = {'transactions': transactions}

        # Load strategy profiles
        strategy_path = os.path.join(base_dir, '../phase_3_rl_agent_training/phase_3_strategy_selector/outputs/strategy_profiles.csv')
        if os.path.exists(strategy_path):
            data['strategy_profiles'] = pd.read_csv(strategy_path)
        else:
            data['strategy_profiles'] = pd.DataFrame()

        # Load token opportunities
        token_path = os.path.join(output_dir, 'token_opportunities.json')
        if os.path.exists(token_path):
            with open(token_path, 'r') as f:
                data['token_opportunities'] = json.load(f)
        else:
            data['token_opportunities'] = {'opportunities': []}

        # Load whale opportunities
        whale_path = os.path.join(output_dir, 'whale_opportunities.json')
        if os.path.exists(whale_path):
            with open(whale_path, 'r') as f:
                data['whale_opportunities'] = json.load(f)
        else:
            data['whale_opportunities'] = {'opportunities': []}

        # REMOVED: Carbon Core simulation data - LIVE TRADING ONLY
        data['carbon_core'] = {}

        logger.info("Successfully loaded dashboard data")
        return data
    except Exception as e:
        logger.error(f"Error loading dashboard data: {str(e)}")
        return {
            'enriched_signals': {'signals': []},
            'tx_history': {'transactions': []},
            'strategy_profiles': pd.DataFrame(),
            'token_opportunities': {'opportunities': []},
            'whale_opportunities': {'opportunities': []},
            'carbon_core': {}  # REMOVED: Carbon Core simulation data
        }

def render_header():
    """Render the dashboard header."""
    col1, col2, col3 = st.columns([1, 2, 1])

    with col1:
        st.image("https://solana.com/_next/static/media/logotype.e4df684f.svg", width=150)

    with col2:
        st.title("🚀 RWA Trading System Live Dashboard")
        st.caption("Live Trading • Orca DEX • Real-time Blockchain Execution")

    with col3:
        st.write(f"🔴 LIVE: {datetime.now().strftime('%H:%M:%S')}")
        if st.button("🔄 Refresh Live Data"):
            st.rerun()

def render_sidebar(data: Dict[str, Any]):
    """
    Render the dashboard sidebar.

    Args:
        data: Dashboard data
    """
    st.sidebar.title("Navigation")

    # Navigation options
    pages = {
        "Overview": "Show system overview and key metrics",
        "Strategy Viewer": "View and analyze trading strategies",
        "Live Monitor": "Monitor live trading activities",
        "PnL Graphs": "View profit and loss graphs",
        "Token Scanner": "Scan for new token opportunities",
        "Whale Watcher": "Monitor whale activities",
        "Settings": "Configure dashboard settings"
    }

    selected_page = st.sidebar.radio("Select Page", list(pages.keys()))

    # Display page description
    st.sidebar.info(pages[selected_page])

    # Display system status
    st.sidebar.title("System Status")

    # Count active strategies
    active_strategies = 0
    if 'strategy_profiles' in data and not data['strategy_profiles'].empty:
        active_strategies = len(data['strategy_profiles'])

    # Count pending signals
    pending_signals = 0
    if 'enriched_signals' in data and 'signals' in data['enriched_signals']:
        pending_signals = len(data['enriched_signals']['signals'])

    # Count recent transactions
    recent_tx = 0
    if 'tx_history' in data and 'transactions' in data['tx_history']:
        recent_tx = sum(1 for tx in data['tx_history']['transactions']
                       if datetime.fromtimestamp(tx.get('timestamp', 0)) > datetime.now() - timedelta(hours=24))

    # Display metrics
    st.sidebar.metric("Active Strategies", active_strategies)
    st.sidebar.metric("Pending Signals", pending_signals)
    st.sidebar.metric("24h Transactions", recent_tx)

    # Display system health
    system_health = "🟢 Healthy" if active_strategies > 0 and recent_tx > 0 else "🟡 Warning" if active_strategies > 0 else "🔴 Offline"
    st.sidebar.success(system_health) if system_health.startswith("🟢") else st.sidebar.warning(system_health) if system_health.startswith("🟡") else st.sidebar.error(system_health)

    return selected_page

def render_overview(data: Dict[str, Any]):
    """
    Render the overview page.

    Args:
        data: Dashboard data
    """
    st.header("System Overview")

    # Key metrics
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        # Count active strategies
        active_strategies = 0
        if 'strategy_profiles' in data and not data['strategy_profiles'].empty:
            active_strategies = len(data['strategy_profiles'])
        st.metric("Active Strategies", active_strategies)

    with col2:
        # Count pending signals
        pending_signals = 0
        if 'enriched_signals' in data and 'signals' in data['enriched_signals']:
            pending_signals = len(data['enriched_signals']['signals'])
        st.metric("Pending Signals", pending_signals)

    with col3:
        # Count recent transactions
        recent_tx = 0
        if 'tx_history' in data and 'transactions' in data['tx_history']:
            recent_tx = sum(1 for tx in data['tx_history']['transactions']
                           if datetime.fromtimestamp(tx.get('timestamp', 0)) > datetime.now() - timedelta(hours=24))
        st.metric("24h Transactions", recent_tx)

    with col4:
        # Calculate success rate
        success_rate = 0
        if 'tx_history' in data and 'transactions' in data['tx_history'] and data['tx_history']['transactions']:
            success_count = sum(1 for tx in data['tx_history']['transactions']
                              if tx.get('status') in ['confirmed', 'finalized'])
            total_count = len(data['tx_history']['transactions'])
            success_rate = success_count / total_count * 100 if total_count > 0 else 0
        st.metric("Transaction Success Rate", f"{success_rate:.1f}%")

    # Recent activity
    st.subheader("Recent Activity")

    if 'tx_history' in data and 'transactions' in data['tx_history'] and data['tx_history']['transactions']:
        # Convert to DataFrame for easier display
        tx_df = pd.DataFrame(data['tx_history']['transactions'])

        # Convert timestamp to datetime
        tx_df['datetime'] = pd.to_datetime(tx_df['timestamp'], unit='s')

        # Sort by timestamp (descending)
        tx_df = tx_df.sort_values('datetime', ascending=False)

        # Display recent transactions
        st.dataframe(tx_df[['datetime', 'signature', 'status']].head(10))
    else:
        st.info("No recent transactions found")

    # Top strategies
    st.subheader("Top Strategies")

    if 'strategy_profiles' in data and not data['strategy_profiles'].empty:
        # Sort by composite score (descending)
        top_strategies = data['strategy_profiles'].sort_values('composite_score', ascending=False).head(5)

        # Display top strategies
        st.dataframe(top_strategies[['strategy_id', 'sharpe_ratio', 'win_rate', 'composite_score']])

        # Plot strategy performance
        fig = px.bar(
            top_strategies,
            x='strategy_id',
            y='composite_score',
            color='sharpe_ratio',
            title='Top Strategy Performance',
            labels={'composite_score': 'Composite Score', 'strategy_id': 'Strategy ID', 'sharpe_ratio': 'Sharpe Ratio'}
        )
        st.plotly_chart(fig)
    else:
        st.info("No strategy profiles found")

def render_token_scanner(data: Dict[str, Any]):
    """
    Render the token scanner page.

    Args:
        data: Dashboard data
    """
    st.header("Token Scanner")

    if 'token_opportunities' in data and 'opportunities' in data['token_opportunities'] and data['token_opportunities']['opportunities']:
        # Convert to DataFrame for easier display
        opportunities = data['token_opportunities']['opportunities']

        # Display opportunities
        st.subheader(f"Found {len(opportunities)} Token Opportunities")

        for i, opp in enumerate(opportunities):
            with st.expander(f"{opp.get('symbol', 'Unknown')} - {opp.get('name', 'Unknown Token')}"):
                col1, col2 = st.columns(2)

                with col1:
                    st.write(f"**Address:** {opp.get('address', 'N/A')}")
                    st.write(f"**Price:** ${opp.get('price', 0):.6f}")
                    st.write(f"**24h Volume:** ${opp.get('volume', 0):,.2f}")

                with col2:
                    st.write(f"**Market Cap:** ${opp.get('marketCap', 0):,.2f}")
                    st.write(f"**Holders:** {opp.get('holders', 0):,}")
                    st.write(f"**Created:** {opp.get('createdAt', 'Unknown')}")

                # Display token details
                if 'details' in opp:
                    st.subheader("Token Details")
                    st.json(opp['details'])
    else:
        st.info("No token opportunities found")

def render_whale_watcher(data: Dict[str, Any]):
    """
    Render the whale watcher page.

    Args:
        data: Dashboard data
    """
    st.header("Whale Watcher")

    if 'whale_opportunities' in data and 'opportunities' in data['whale_opportunities'] and data['whale_opportunities']['opportunities']:
        # Convert to DataFrame for easier display
        opportunities = data['whale_opportunities']['opportunities']

        # Display opportunities
        st.subheader(f"Found {len(opportunities)} Whale Activities")

        for i, opp in enumerate(opportunities):
            with st.expander(f"Token: {opp.get('token_address', 'Unknown')} - {opp.get('whale_transaction_count', 0)} transactions"):
                st.write(f"**Whale Transaction Count:** {opp.get('whale_transaction_count', 0)}")

                # Display activity
                if 'activity' in opp and opp['activity']:
                    st.subheader("Recent Activity")

                    # Convert to DataFrame for easier display
                    activity_df = pd.DataFrame(opp['activity'])

                    # Display activity
                    st.dataframe(activity_df)
    else:
        st.info("No whale activities found")

def render_settings():
    """Render the settings page."""
    st.header("Dashboard Settings")

    # Theme settings
    st.subheader("Theme Settings")
    theme = st.selectbox("Select Theme", ["Light", "Dark"])

    # Notification settings
    st.subheader("Notification Settings")
    st.checkbox("Enable Email Notifications", value=False)
    st.checkbox("Enable Slack Notifications", value=False)
    st.checkbox("Enable Telegram Notifications", value=False)

    # Auto-refresh settings
    st.subheader("Auto-Refresh Settings")
    refresh_interval = st.slider("Refresh Interval (seconds)", 0, 300, 60)

    # Save settings
    if st.button("Save Settings"):
        st.success("Settings saved successfully!")

def main():
    """Main function to run the dashboard."""
    # Load data
    data = load_data()

    # Render header
    render_header()

    # Render sidebar and get selected page
    selected_page = render_sidebar(data)

    # Render selected page
    if selected_page == "Overview":
        render_overview(data)
    elif selected_page == "Strategy Viewer":
        render_strategy_viewer(data)
    elif selected_page == "Live Monitor":
        render_live_monitor(data)
    elif selected_page == "PnL Graphs":
        render_pnl_graphs(data)
    elif selected_page == "Token Scanner":
        render_token_scanner(data)
    elif selected_page == "Whale Watcher":
        render_whale_watcher(data)
    elif selected_page == "Settings":
        render_settings()

if __name__ == "__main__":
    main()
