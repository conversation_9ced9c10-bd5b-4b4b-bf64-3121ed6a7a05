#!/usr/bin/env python3
"""
Complete Jupiter Removal <PERSON>t
Removes all Jupiter-related files, imports, and dependencies from the system.
Keeps only QuickNode/Jito bundle support.
"""

import os
import shutil
import logging
import re

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def remove_jupiter_files():
    """Remove all Jupiter-related files."""
    
    jupiter_files = [
        # Jupiter clients and builders
        "phase_4_deployment/utils/modern_jupiter_client.py",
        "phase_4_deployment/utils/jupiter_swap_fallback.py",
        "deprecated_builders/jupiter_swap_fallback.py",
        
        # Jupiter test files
        "scripts/test_jupiter_swap.py",
        "scripts/direct_jupiter_test.py",
        "scripts/fix_jupiter_signature_issue.py",
        "scripts/test_simple_jupiter_fix.py",
        
        # Jupiter configuration files
        "config/jupiter_config.yaml",
        "config/jupiter_timing_fix.yaml",
        
        # Jupiter builders in backups
        "backups/cleanup_backup_20250526_233411/phase_4_deployment/rpc_execution/enhanced_jupiter_builder.py",
        "backups/cleanup_backup_20250526_233411/phase_4_deployment/rpc_execution/immediate_jupiter_builder.py",
        "backups/pre_modern_integration/phase_4_deployment/utils/jupiter_swap_fallback.py",
        
        # Any other Jupiter-related files
        "phase_4_deployment/rpc_execution/enhanced_jupiter_builder.py",
        "phase_4_deployment/rpc_execution/immediate_jupiter_builder.py",
    ]
    
    removed_files = []
    
    for file_path in jupiter_files:
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                removed_files.append(file_path)
                logger.info(f"🗑️ REMOVED: {file_path}")
            except Exception as e:
                logger.error(f"❌ Failed to remove {file_path}: {e}")
        else:
            logger.info(f"⚠️ Not found: {file_path}")
    
    return removed_files

def remove_jupiter_imports():
    """Remove Jupiter imports from Python files."""
    
    files_to_check = [
        "scripts/unified_live_trading.py",
        "core/dex/__init__.py",
        "core/dex/unified_transaction_builder.py",
        "core/dex/orca_client.py",
        "phase_4_deployment/rpc_execution/modern_transaction_executor.py",
    ]
    
    jupiter_import_patterns = [
        r"from.*jupiter.*import.*",
        r"import.*jupiter.*",
        r"from.*ModernJupiterClient.*import.*",
        r".*ModernJupiterClient.*",
        r".*jupiter_client.*",
        r".*jupiter_swap.*",
    ]
    
    modified_files = []
    
    for file_path in files_to_check:
        if not os.path.exists(file_path):
            continue
            
        try:
            with open(file_path, 'r') as f:
                content = f.read()
            
            original_content = content
            lines = content.split('\n')
            new_lines = []
            
            for line in lines:
                # Check if line contains Jupiter references
                is_jupiter_line = False
                for pattern in jupiter_import_patterns:
                    if re.search(pattern, line, re.IGNORECASE):
                        is_jupiter_line = True
                        logger.info(f"🗑️ Removing Jupiter line from {file_path}: {line.strip()}")
                        break
                
                if not is_jupiter_line:
                    new_lines.append(line)
            
            new_content = '\n'.join(new_lines)
            
            if new_content != original_content:
                with open(file_path, 'w') as f:
                    f.write(new_content)
                modified_files.append(file_path)
                logger.info(f"✅ Updated {file_path} - removed Jupiter imports")
        
        except Exception as e:
            logger.error(f"❌ Failed to process {file_path}: {e}")
    
    return modified_files

def update_depr_txt(removed_files):
    """Update depr.txt with removed Jupiter files."""
    
    try:
        with open("depr.txt", "a") as f:
            f.write("\n# Jupiter Integration Completely Removed\n")
            f.write("# Replaced with native QuickNode/Jito bundle support\n")
            for file_path in removed_files:
                f.write(f"# REMOVED: {file_path}\n")
            f.write(f"# Removal date: {os.popen('date').read().strip()}\n")
        logger.info("✅ Updated depr.txt with removed Jupiter files")
    except Exception as e:
        logger.error(f"❌ Failed to update depr.txt: {e}")

def create_jupiter_free_summary():
    """Create a summary of the Jupiter-free system."""
    
    summary = """
# JUPITER-FREE SYSTEM SUMMARY

## 🚀 ARCHITECTURE AFTER JUPITER REMOVAL

### **Primary Stack (QuickNode + Jito + Native)**
- ✅ **QuickNode**: Primary RPC with bundle support
- ✅ **Jito**: MEV protection and atomic execution  
- ✅ **Native Swaps**: Direct transaction building
- ✅ **Helius**: Fallback RPC and balance checking

### **Transaction Flow (Jupiter-Free)**
1. **Signal Generation** → Strategy selection
2. **Native Builder** → Direct transaction construction
3. **Bundle Executor** → QuickNode/Jito bundles
4. **Balance Validation** → Real change detection
5. **Telegram Alerts** → Success/failure notifications

### **Removed Jupiter Components**
- ❌ Jupiter API clients
- ❌ Jupiter swap builders
- ❌ Jupiter configuration files
- ❌ Jupiter test scripts
- ❌ Jupiter import statements

### **Benefits of Jupiter Removal**
- ✅ **Simplified Architecture**: No external API dependencies
- ✅ **Faster Execution**: Direct transaction building
- ✅ **Better Reliability**: No 400 Bad Request errors
- ✅ **Reduced Complexity**: Fewer moving parts
- ✅ **Native Control**: Full control over transaction building

### **Current Entry Point**
- **Main**: `scripts/unified_live_trading.py`
- **Builder**: `core/dex/native_swap_builder.py`
- **Executor**: `phase_4_deployment/rpc_execution/modern_transaction_executor.py`
"""
    
    try:
        with open("JUPITER_FREE_SYSTEM.md", "w") as f:
            f.write(summary)
        logger.info("✅ Created JUPITER_FREE_SYSTEM.md")
    except Exception as e:
        logger.error(f"❌ Failed to create summary: {e}")

def main():
    """Main Jupiter removal process."""
    
    logger.info("🚀 STARTING COMPLETE JUPITER REMOVAL")
    logger.info("=" * 50)
    
    # Step 1: Remove Jupiter files
    logger.info("📁 Removing Jupiter files...")
    removed_files = remove_jupiter_files()
    
    # Step 2: Remove Jupiter imports
    logger.info("🔧 Removing Jupiter imports...")
    modified_files = remove_jupiter_imports()
    
    # Step 3: Update documentation
    logger.info("📝 Updating documentation...")
    update_depr_txt(removed_files)
    create_jupiter_free_summary()
    
    # Summary
    logger.info("=" * 50)
    logger.info("🎉 JUPITER REMOVAL COMPLETE!")
    logger.info(f"📊 Files removed: {len(removed_files)}")
    logger.info(f"📊 Files modified: {len(modified_files)}")
    logger.info("🚀 System now uses native QuickNode/Jito bundles only")
    logger.info("✅ No more Jupiter API dependencies!")

if __name__ == "__main__":
    main()
