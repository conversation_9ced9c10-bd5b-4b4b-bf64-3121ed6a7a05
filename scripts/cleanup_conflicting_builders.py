#!/usr/bin/env python3
"""
Cleanup Conflicting Transaction Builders
Moves conflicting transaction builders to deprecated folder to prevent confusion.
"""

import os
import shutil
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def cleanup_conflicting_builders():
    """Move conflicting transaction builders to deprecated folder."""
    
    # Create deprecated folder
    deprecated_dir = "deprecated_builders"
    os.makedirs(deprecated_dir, exist_ok=True)
    
    # List of conflicting builders to deprecate
    conflicting_builders = [
        "phase_4_deployment/rpc_execution/tx_builder.py",
        "core/dex/orca_swap_builder.py",
        "phase_4_deployment/utils/jupiter_swap_fallback.py",
        "backups/tx_builder_backup.py",
        "backups/orca_swap_builder_backup.py"
    ]
    
    moved_files = []
    
    for builder_path in conflicting_builders:
        if os.path.exists(builder_path):
            try:
                # Create destination path
                filename = os.path.basename(builder_path)
                dest_path = os.path.join(deprecated_dir, filename)
                
                # Move file
                shutil.move(builder_path, dest_path)
                moved_files.append(f"{builder_path} → {dest_path}")
                logger.info(f"✅ Moved {builder_path} to {dest_path}")
                
            except Exception as e:
                logger.error(f"❌ Failed to move {builder_path}: {e}")
        else:
            logger.info(f"⚠️ File not found: {builder_path}")
    
    # Update depr.txt
    try:
        with open("depr.txt", "a") as f:
            f.write("\n# Conflicting Transaction Builders (Replaced by UnifiedTransactionBuilder)\n")
            for moved_file in moved_files:
                f.write(f"# {moved_file}\n")
            f.write(f"# Cleanup date: {os.popen('date').read().strip()}\n")
        logger.info("✅ Updated depr.txt with moved files")
    except Exception as e:
        logger.error(f"❌ Failed to update depr.txt: {e}")
    
    logger.info(f"🎯 CLEANUP COMPLETE: Moved {len(moved_files)} conflicting builders")
    logger.info("🚀 System now uses UnifiedTransactionBuilder exclusively")

if __name__ == "__main__":
    cleanup_conflicting_builders()
