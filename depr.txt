# SYSTEM CONSOLIDATED TO 3 ENTRY POINTS
# All redundant files have been removed

# === 3 SINGLE ENTRY POINTS ===
# 1. Live Trading: scripts/unified_live_trading.py
# 2. Backtesting: phase_4_deployment/unified_runner.py --mode backtest
# 3. Paper Trading: phase_4_deployment/unified_runner.py --mode paper

# === DELETED REDUNDANT FILES ===
# All files listed below have been removed from the system
# to eliminate confusion and maintain clean architecture

# core/transaction/enhanced_tx_builder.py
# core/transaction/enhanced_tx_executor.py
# scripts/enhanced_live_trading.py
# scripts/test_live_orca_fix.py
# scripts/test_orca_price_fix.py
# scripts/test_fixed_live_trading.py
# scripts/run_fixed_live_trading.py
# scripts/deploy_live_production.py
# scripts/run_48_hour_live_trading.py
# scripts/test_live_production_deployment.py
# scripts/test_live_trading_fixed.py
# scripts/single_live_trade_test.py
# phase_4_deployment/start_live_trading.py
# phase_4_deployment/run_live_integration.py
# phase_4_deployment/run_complete_system.py
# phase_4_deployment/run_trading_system.py
# phase_4_deployment/run_paper_trading.py

# === DIAGNOSTIC SCRIPTS (TEMPORARY) ===
# scripts/diagnose_signal_generation.py
# scripts/test_api_transaction_fixes.py
# phase_4_deployment/run_simulation.py
# phase_4_deployment/run_active_simulation.py
# phase_4_deployment/scripts/run_live_trade_test.py
# phase_4_deployment/scripts/test_real_transaction.py
# phase_4_deployment/scripts/test_trade.py
# phase_4_deployment/scripts/run_paper_trade.py
# phase_4_deployment/scripts/simulation_test.py
# phase_4_deployment/scripts/run_simulation_test.py
# phase_4_deployment/scripts/verify_simulation.py
# scripts/test_orca_integration.py
# scripts/test_end_to_end_system.py
# scripts/test_100_percent_ready.py
# scripts/test_base64_encoding_fix.py
# scripts/test_comprehensive_alerts.py
# scripts/test_dashboard_metrics.py
# scripts/test_dashboard_performance.py
# scripts/test_direct_transaction.py
# scripts/test_dual_telegram.py
# scripts/test_jito_signature_verification_fix.py
# scripts/test_live_alerts_integration.py
# scripts/test_risk_components.py
# scripts/test_serialization_fix.py
# scripts/test_signature_fix.py
# scripts/test_simple_transaction.py
# scripts/test_telegram_alerts.py
# scripts/testnet_system_validation.py
# scripts/enhanced_dashboard_sync.py
# core/notifications/enhanced_telegram_wrapper.py
# scripts/analyze_system_files.py
# scripts/wallet_scaling_analysis.py
# scripts/compare_strategies.py
# scripts/deploy_fixed_jupiter_system.py
# phase_4_deployment/deploy.py
# phase_4_deployment/update_deployment.py
# phase_4_deployment/setup_environment.py
# scripts/cleanup_deprecated_files.py
# scripts/cleanup_jupiter_files.py
# scripts/cleanup_redundant_wallets.py
# scripts/create_focused_depr_list.py
# scripts/fix_jupiter_blockhash_timing.py
# scripts/fix_signature_verification.py
# scripts/fix_testnet_keypair.py
# scripts/setup_jupiter_config.py
# scripts/setup_testnet_environment.py
# scripts/simple_testnet_test.py
# scripts/simple_trade_test.py
# scripts/integration_test.py
# scripts/system_test.py
# scripts/validate_trading_system.py
# scripts/verify_simulation.py

# === ESSENTIAL FILES KEPT ===
# These are the only entry points and essential support files

# scripts/unified_live_trading.py
# phase_4_deployment/unified_runner.py
# scripts/comprehensive_system_test.py
# scripts/final_production_verification.py
# scripts/system_status_check.py
# scripts/rich_trade_analyzer.py
# scripts/analyze_trades.py
# scripts/analyze_live_metrics_profitability.py
# phase_4_deployment/deploy_production.sh
# phase_4_deployment/docker_deploy/entrypoint.sh
# phase_4_deployment/dashboard/streamlit_dashboard.py
# phase_4_deployment/monitoring/health_check_server.py

# === PRODUCTION CLEANUP - 39 FILES REMOVED ===
# Removed on production cleanup to shed weight from beautifully running system
# All files below were outdated/irrelevant to current profitable system

# tests/test_carbon_core_integration.py
# tests/test_tx_prep_service.py
# tests/test_signature_verification_fix.py
# tests/test_wallet_security.py
# tests/test_momentum_optimizer.py
# tests/run_comprehensive_tests.py
# tests/run_tests.sh
# scripts/debug_transaction_execution.py

# === SIMULATION AND MOCK FILES - REMOVED FOR LIVE TRADING ===
# All simulation, placeholder, and mock components removed to ensure
# only real trading execution occurs in the live trading system
phase_4_deployment/scripts/simple_dry_run.py
phase_4_deployment/unified_dashboard/simulation.py
phase_4_deployment/python_comm_layer/mock_carbon_core.py
phase_4_deployment/utils/mock_signal_generator.py
scripts/generate_test_live_data.py
phase_4_deployment/scripts/helius_dry_run.py
# scripts/diagnose_signal_generation.py
# scripts/fix_jupiter_signature_issue.py
# scripts/quick_signature_test.py
# scripts/test_signature_verification.py
# scripts/test_simple_jupiter_fix.py
# scripts/test_api_transaction_fixes.py
# scripts/test_fixed_trading_system.py
# scripts/test_live_trading_rollback.py
# scripts/test_rollback.py
# scripts/test_rpc_endpoints.py
# scripts/verify_balance_change.py
# scripts/analyze_trades.py
# scripts/check_live_trading_status.py
# scripts/monitor_wallet_balance.py
# scripts/run_terminal_metrics.py
# scripts/sync_live_dashboard_metrics.py
# scripts/update_dashboard_for_production.py
# scripts/update_dashboard_real_balance.py
# scripts/auto_sync_dashboard.py
# scripts/reset_dashboard_metrics.py
# scripts/reset_all_metrics.py
# scripts/create_keypair_from_env.py
# scripts/direct_keypair_creation.py
# scripts/generate_test_keypair.py
# scripts/import_wallet.py
# scripts/validate_config.py
# scripts/test_unified_config.py
# scripts/optimize_momentum.py
# scripts/restart_opportunistic_with_roi.py
# scripts/purge_mean_reversion.py
# scripts/emergency_position_flattener.py

# Conflicting Transaction Builders (Replaced by UnifiedTransactionBuilder)
# phase_4_deployment/rpc_execution/tx_builder.py → deprecated_builders/tx_builder.py
# core/dex/orca_swap_builder.py → deprecated_builders/orca_swap_builder.py
# phase_4_deployment/utils/jupiter_swap_fallback.py → deprecated_builders/jupiter_swap_fallback.py
# Cleanup date: Fri May 30 17:30:40 PDT 2025

# Jupiter Integration Completely Removed
# Replaced with native QuickNode/Jito bundle support
# REMOVED: phase_4_deployment/utils/modern_jupiter_client.py
# REMOVED: deprecated_builders/jupiter_swap_fallback.py
# REMOVED: backups/pre_modern_integration/phase_4_deployment/utils/jupiter_swap_fallback.py
# Removal date: Fri May 30 17:40:21 PDT 2025
